<template>
  <div class="label-hiprint-page">
    <!-- 页面头部 -->
    <el-card shadow="never" class="header-card">
      <template #header>
        <div class="header-content">
          <span class="title">标签模板设计器</span>
          <div class="header-actions">
            <el-button
              type="primary"
              :icon="Plus"
              @click="handleNewTemplate"
            >
              新建模板
            </el-button>
            <el-button
              :icon="Refresh"
              @click="loadTemplateList"
              :loading="loading"
            >
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 模板列表 -->
      <div class="template-list">
        <el-row :gutter="16">
          <el-col
            v-for="template in templateList"
            :key="template.id"
            :span="6"
          >
            <el-card
              shadow="hover"
              class="template-card"
              :class="{ 'active': currentTemplate?.id === template.id }"
              @click="selectTemplate(template)"
            >
              <div class="template-info">
                <div class="template-name">
                  {{ template.templateName }}
                  <el-tag v-if="template.isDefault" type="success" size="small">默认</el-tag>
                </div>
                <div class="template-meta">
                  <span class="template-type">{{ getTemplateTypeText(template.templateType) }}</span>
                  <span class="template-time">{{ formatTime(template.createTime) }}</span>
                </div>
              </div>
              <div class="template-actions">
                <el-button
                  type="primary"
                  size="small"
                  @click.stop="editTemplate(template)"
                >
                  编辑
                </el-button>
                <el-button
                  type="success"
                  size="small"
                  @click.stop="previewTemplate(template)"
                >
                  预览
                </el-button>
                <el-button
                  v-if="!template.isDefault"
                  type="danger"
                  size="small"
                  @click.stop="deleteTemplate(template)"
                >
                  删除
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 设计器抽屉 -->
    <el-drawer
      v-model="showDesigner"
      title=""
      direction="rtl"
      size="80%"
      :before-close="handleDrawerClose"
      class="designer-drawer"
    >
      <template #header>
        <div class="designer-header">
          <div class="designer-title">
            <span>{{ isEditMode ? '编辑模板' : '新建模板' }}: </span>
            <EditableTemplateName
              v-model="currentTemplateName"
              :placeholder="'未命名模板'"
              @change="handleTemplateNameChange"
            />
          </div>
          <div class="designer-actions">
            <el-button @click="handleSaveTemplate" type="primary" :loading="saving">
              保存模板
            </el-button>
            <el-button @click="handlePreview" type="success">
              预览效果
            </el-button>
            <el-button @click="closeDesigner">
              关闭设计器
            </el-button>
          </div>
        </div>
      </template>

      <div class="designer-content">
        <!-- 纸张设置工具栏 -->
        <PaperSettings
          ref="paperSettingsRef"
          :hiprint-template="hiprintTemplate"
          @paper-change="handlePaperChange"
          @orientation-change="handleOrientationChange"
        />

        <!-- hiprint 设计器容器 -->
        <div class="hiprint-container">
          <div class="hiprint-panel-left">
            <!-- 左侧面板：元素库 -->
            <ElementLibrary />
          </div>
          <div
            id="hiprint-panel-center"
            class="hiprint-panel-center"
          >
            <!-- 中间面板：设计区域 -->
          </div>
          <div class="hiprint-panel-right">
            <!-- 右侧面板：hiprint官方属性设置 -->
            <div id="PrintElementOptionSetting" class="hiprint-option-setting">
              <!-- hiprint会自动在这里渲染属性面板 -->
            </div>
          </div>
        </div>
      </div>
    </el-drawer>

    <!-- 模板保存对话框 -->
    <el-dialog
      v-model="showSaveDialog"
      title="保存模板"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="saveFormRef"
        :model="saveForm"
        :rules="saveRules"
        label-width="100px"
      >
        <el-form-item label="模板名称" prop="templateName">
          <el-input
            v-model="saveForm.templateName"
            placeholder="请输入模板名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="模板类型" prop="templateType">
          <el-select v-model="saveForm.templateType" placeholder="请选择模板类型">
            <el-option label="自定义模板" value="CUSTOM" />
            <el-option label="系统模板" value="SYSTEM" />
          </el-select>
        </el-form-item>
        <el-form-item label="设为默认">
          <el-switch v-model="saveForm.isDefault" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="saveForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入模板描述"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showSaveDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmSaveTemplate" :loading="saving">
          确定保存
        </el-button>
      </template>
    </el-dialog>

    <!-- 预览模态框 -->
    <PreviewModal
      ref="previewModalRef"
      v-model="showPreviewDialog"
      :title="previewTitle"
      :template-info="previewTemplateInfo"
      :width="previewDialogWidth"
      @print="handlePreviewPrint"
      @export-pdf="handlePreviewExportPdf"
      @refresh="handlePreviewRefresh"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue';
import { Plus, Refresh, ArrowDown } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus/es';
import { EleMessage } from 'ele-admin-plus/es';
import dayjs from 'dayjs';

// 组件导入
import ElementLibrary from './components/ElementLibrary.vue';
import PaperSettings from './components/PaperSettings.vue';
import EditableTemplateName from './components/EditableTemplateName.vue';
import PreviewModal from './components/PreviewModal.vue';


// API 导入
import {
  getTemplateList,
  saveTemplate,
  deleteTemplate as deleteTemplateApi,
  getAvailableFields,
  getFieldsByCategory,
  previewLabel
} from './api';

// hiprint 相关导入
import {
  initHiprint as initHiprintPlugin,
  createPrintTemplate,
  createPreviewTemplate,
  getTemplatePaperInfo,
  detectPaperTypeAndOrientation,
  designerConfig,
  handleHiprintError,
  getFieldElementTemplate,
  safeAddElement,
  detectHiprintAPI,
  buildDraggableElements,
  setPaperSize,
  setElementSelectCallback,
  setElementChangeCallback,
  updateElementProperty,
  copyElement,
  deleteElement,
  getHiprintInstance,
  cleanupTemplate
} from '@/utils/hiprint-config';

// 响应式数据
const loading = ref(false);
const saving = ref(false);
const templateList = ref([]);
const currentTemplate = ref(null);
const showDesigner = ref(false);
const isEditMode = ref(false);
const showSaveDialog = ref(false);
const showPreviewDialog = ref(false);

// 预览相关数据
const previewDialogWidth = ref('80%');
const previewTitle = ref('模板预览');
const previewTemplateInfo = ref('');

// 组件引用
const paperSettingsRef = ref();
const previewModalRef = ref();


// hiprint 实例
let hiprintTemplate = null;
let availableFields = [];

// 当前模板名称
const currentTemplateName = ref('');

// 保存表单
const saveFormRef = ref();
const saveForm = reactive({
  templateName: '',
  templateType: 'CUSTOM',
  isDefault: false,
  description: ''
});

const saveRules = {
  templateName: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 2, max: 50, message: '模板名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  templateType: [
    { required: true, message: '请选择模板类型', trigger: 'change' }
  ]
};

// 方法定义
const loadTemplateList = async () => {
  loading.value = true;
  try {
    const data = await getTemplateList();
    templateList.value = data || [];
  } catch (error) {
    EleMessage.error('加载模板列表失败：' + error.message);
  } finally {
    loading.value = false;
  }
};

const loadAvailableFields = async () => {
  try {
    availableFields = await getFieldsByCategory();
  } catch (error) {
    console.error('加载可用字段失败:', error);
  }
};

const selectTemplate = (template) => {
  currentTemplate.value = template;
};

const handleNewTemplate = () => {
  currentTemplate.value = null;
  currentTemplateName.value = '';
  isEditMode.value = false;
  showDesigner.value = true;
  nextTick(() => {
    initDesigner();
  });
};

const editTemplate = (template) => {
  console.log('开始编辑模板:', template);

  // 清理之前的设计器实例
  if (hiprintTemplate) {
    cleanupTemplate(hiprintTemplate);
    hiprintTemplate = null;
  }

  // 设置编辑状态
  currentTemplate.value = template;
  currentTemplateName.value = template.templateName || '';
  isEditMode.value = true;
  showDesigner.value = true;

  // 初始化保存表单数据（确保编辑时表单有正确的默认值）
  if (template) {
    saveForm.templateName = template.templateName || '';
    saveForm.templateType = template.templateType || 'CUSTOM';
    saveForm.isDefault = template.isDefault || false;
    saveForm.description = template.description || '';
  }

  // 等待DOM更新后初始化设计器
  nextTick(async () => {
    try {
      await initDesigner(template);
      console.log('编辑模式设计器初始化完成');
    } catch (error) {
      console.error('编辑模式设计器初始化失败:', error);
      EleMessage.error('加载模板失败：' + error.message);
    }
  });
};

const closeDesigner = () => {
  showDesigner.value = false;
  if (hiprintTemplate) {
    cleanupTemplate(hiprintTemplate);
    hiprintTemplate = null;
  }
};



const handleSaveTemplate = () => {
  if (!hiprintTemplate) {
    EleMessage.error('请先设计模板内容');
    return;
  }

  // 获取模板数据
  const templateData = hiprintTemplate.getJson();
  if (!templateData || !templateData.panels || templateData.panels.length === 0) {
    EleMessage.error('模板内容不能为空');
    return;
  }

  // 填充保存表单
  if (isEditMode.value && currentTemplate.value) {
    saveForm.templateName = currentTemplateName.value || currentTemplate.value.templateName;
    saveForm.templateType = currentTemplate.value.templateType;
    saveForm.isDefault = currentTemplate.value.isDefault;
    saveForm.description = currentTemplate.value.description || '';
  } else {
    saveForm.templateName = currentTemplateName.value || '';
    saveForm.templateType = 'CUSTOM';
    saveForm.isDefault = false;
    saveForm.description = '';
  }

  showSaveDialog.value = true;
};

const confirmSaveTemplate = async () => {
  try {
    await saveFormRef.value.validate();

    saving.value = true;

    const templateData = hiprintTemplate.getJson();

    // 获取当前纸张设置
    const paperSettings = paperSettingsRef.value ? {
      paperInfo: paperSettingsRef.value.getCurrentPaper(),
      orientation: paperSettingsRef.value.getOrientation(),
      timestamp: new Date().toISOString()
    } : null;

    const saveData = {
      id: isEditMode.value ? currentTemplate.value?.id : undefined,
      templateName: saveForm.templateName,
      templateType: saveForm.templateType,
      layoutConfig: JSON.stringify(templateData),
      pageSettings: paperSettings ? JSON.stringify(paperSettings) : null,
      isDefault: saveForm.isDefault,
      description: saveForm.description,
      status: 'ACTIVE'
    };

    console.log('保存模板数据:', saveData);

    await saveTemplate(saveData);

    EleMessage.success('模板保存成功');
    showSaveDialog.value = false;
    await loadTemplateList();

  } catch (error) {
    EleMessage.error('保存失败：' + error.message);
  } finally {
    saving.value = false;
  }
};

const handlePreview = async () => {
  if (!hiprintTemplate) {
    EleMessage.error('请先设计模板内容');
    return;
  }

  try {
    // 获取模板数据验证是否有内容
    const templateData = hiprintTemplate.getJson();
    if (!templateData || !templateData.panels || templateData.panels.length === 0) {
      EleMessage.warning('模板内容为空，请先添加一些元素');
      return;
    }

    // 设置预览信息
    previewTitle.value = isEditMode.value ? '编辑模板预览' : '新建模板预览';
    previewTemplateInfo.value = currentTemplateName.value || '未命名模板';

    // 获取模板尺寸并设置对话框大小
    setPreviewDialogSize();

    // 使用新的预览组件显示预览
    if (previewModalRef.value) {
      await previewModalRef.value.show(hiprintTemplate, {}, {
        width: previewDialogWidth.value
      });
    }
  } catch (error) {
    console.error('预览失败:', error);
    EleMessage.error('预览失败: ' + error.message);
  }
};

// 设置预览对话框尺寸
const setPreviewDialogSize = (templateInstance = null, templateData = null) => {
  try {
    let panelWidth = 210; // 默认A4宽度(mm)
    let panelHeight = 297; // 默认A4高度(mm)

    // 如果传入了模板实例，优先使用模板实例
    if (templateInstance) {
      // 尝试从 editingPanel 获取尺寸
      if (templateInstance.editingPanel && templateInstance.editingPanel.width && templateInstance.editingPanel.height) {
        panelWidth = templateInstance.editingPanel.width;
        panelHeight = templateInstance.editingPanel.height;
        console.log('从模板实例 editingPanel 获取尺寸:', { width: panelWidth, height: panelHeight });
      }
      // 备选方案：从第一个面板获取尺寸
      else if (templateInstance.panels && templateInstance.panels.length > 0) {
        const firstPanel = templateInstance.panels[0];
        if (firstPanel.width && firstPanel.height) {
          panelWidth = firstPanel.width;
          panelHeight = firstPanel.height;
          console.log('从模板实例第一个面板获取尺寸:', { width: panelWidth, height: panelHeight });
        }
      }
      // 最后尝试从模板数据获取
      else if (typeof templateInstance.getJson === 'function') {
        const jsonData = templateInstance.getJson();
        if (jsonData && jsonData.panels && jsonData.panels.length > 0) {
          const panel = jsonData.panels[0];
          if (panel.width && panel.height) {
            panelWidth = panel.width;
            panelHeight = panel.height;
            console.log('从模板实例JSON数据获取尺寸:', { width: panelWidth, height: panelHeight });
          }
        }
      }
    }
    // 如果没有模板实例但有全局的 hiprintTemplate
    else if (hiprintTemplate) {
      // 尝试从 editingPanel 获取尺寸
      if (hiprintTemplate.editingPanel && hiprintTemplate.editingPanel.width && hiprintTemplate.editingPanel.height) {
        panelWidth = hiprintTemplate.editingPanel.width;
        panelHeight = hiprintTemplate.editingPanel.height;
        console.log('从全局 hiprintTemplate editingPanel 获取尺寸:', { width: panelWidth, height: panelHeight });
      }
      // 备选方案：从第一个面板获取尺寸
      else if (hiprintTemplate.panels && hiprintTemplate.panels.length > 0) {
        const firstPanel = hiprintTemplate.panels[0];
        if (firstPanel.width && firstPanel.height) {
          panelWidth = firstPanel.width;
          panelHeight = firstPanel.height;
          console.log('从全局 hiprintTemplate 第一个面板获取尺寸:', { width: panelWidth, height: panelHeight });
        }
      }
      // 最后尝试从模板数据获取
      else {
        const jsonData = hiprintTemplate.getJson();
        if (jsonData && jsonData.panels && jsonData.panels.length > 0) {
          const panel = jsonData.panels[0];
          if (panel.width && panel.height) {
            panelWidth = panel.width;
            panelHeight = panel.height;
            console.log('从全局 hiprintTemplate JSON数据获取尺寸:', { width: panelWidth, height: panelHeight });
          }
        }
      }
    }
    // 如果传入了模板数据，直接从数据中获取
    else if (templateData && templateData.panels && templateData.panels.length > 0) {
      const panel = templateData.panels[0];
      if (panel.width && panel.height) {
        panelWidth = panel.width;
        panelHeight = panel.height;
        console.log('从传入的模板数据获取尺寸:', { width: panelWidth, height: panelHeight });
      }
    }

    // 计算对话框尺寸（考虑屏幕尺寸和边距）
    const screenWidth = window.innerWidth;
    const screenHeight = window.innerHeight;

    // 将mm转换为px（大约1mm = 3.78px）
    const mmToPx = 3.78;
    const panelWidthPx = panelWidth * mmToPx;
    const panelHeightPx = panelHeight * mmToPx;

    // 添加对话框边距和内边距（大约200px）
    const dialogPadding = 200;
    const dialogWidthPx = panelWidthPx + dialogPadding;
    const dialogHeightPx = panelHeightPx + dialogPadding;

    // 计算对话框宽度（不超过屏幕宽度的90%）
    const maxDialogWidth = screenWidth * 0.9;
    const finalDialogWidth = Math.min(dialogWidthPx, maxDialogWidth);

    // 计算对话框高度（不超过屏幕高度的90%）
    const maxDialogHeight = screenHeight * 0.9;
    const finalDialogHeight = Math.min(dialogHeightPx, maxDialogHeight);

    // 设置对话框尺寸
    if (finalDialogWidth < screenWidth * 0.5) {
      // 如果计算出的宽度太小，使用百分比
      previewDialogWidth.value = '50%';
    } else {
      // 使用像素值
      previewDialogWidth.value = `${finalDialogWidth}px`;
    }

    // 设置对话框样式（包括高度）
    previewDialogStyle.value = {
      maxHeight: `${finalDialogHeight}px`,
      height: 'auto'
    };

    console.log('设置预览对话框尺寸:', {
      panelSize: `${panelWidth}mm × ${panelHeight}mm`,
      dialogWidth: previewDialogWidth.value,
      dialogStyle: previewDialogStyle.value
    });

  } catch (error) {
    console.error('设置预览对话框尺寸失败:', error);
    // 使用默认尺寸
    previewDialogWidth.value = '80%';
  }
};

// 预览打印处理
const handlePreviewPrint = async ({ template, data }) => {
  try {
    if (typeof template.print === 'function') {
      // 使用hiprint的直接打印功能
      template.print(data, {}, {
        callback: () => {
          console.log('打印完成');
          EleMessage.success('打印任务已发送');
        }
      });
    } else {
      // 备选方案：打开新窗口进行打印
      const printWindow = window.open('', '_blank');
      const htmlContent = template.getHtml(data);

      const printHTML = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>打印预览</title>
          <style>
            body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
            @media print { body { padding: 0; } }
          </style>
        </head>
        <body>
          ${htmlContent[0].innerHTML}
        </body>
        </html>
      `;

      printWindow.document.write(printHTML);
      printWindow.document.close();
      printWindow.onload = () => {
        printWindow.print();
        printWindow.close();
      };
    }
  } catch (error) {
    console.error('打印失败:', error);
    EleMessage.error('打印失败: ' + error.message);
  }
};

// 预览导出PDF处理
const handlePreviewExportPdf = async ({ template, data }) => {
  try {
    if (typeof template.toPdf === 'function') {
      // 使用hiprint的PDF导出功能
      const fileName = `${currentTemplateName.value || '模板预览'}_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.pdf`;
      await template.toPdf(data, fileName, { isDownload: true });
      EleMessage.success('PDF导出成功');
    } else {
      throw new Error('模板不支持PDF导出功能');
    }
  } catch (error) {
    console.error('导出PDF失败:', error);
    EleMessage.error('导出PDF失败: ' + error.message);
  }
};

// 预览刷新处理
const handlePreviewRefresh = () => {
  console.log('预览已刷新');
};

const previewTemplate = async (template) => {
  try {
    // 确保 hiprint 已初始化
    await initHiprintPlugin();

    // 解析模板数据以获取尺寸信息
    let templateData = null;
    let tempTemplate = null;

    if (template.layoutConfig) {
      try {
        templateData = JSON.parse(template.layoutConfig);
        // 创建预览专用的模板实例
        tempTemplate = createPreviewTemplate(templateData);

        // 根据模板尺寸设置对话框大小
        setPreviewDialogSize(tempTemplate, templateData);

      } catch (parseError) {
        console.error('解析模板数据失败:', parseError);
        // 使用默认尺寸
        setPreviewDialogSize(null, null);
      }
    } else {
      // 使用默认尺寸
      setPreviewDialogSize(null, null);
    }

    // 设置预览信息
    previewTitle.value = '模板预览';
    previewTemplateInfo.value = template.name || '未命名模板';

    // 使用新的预览组件显示预览
    if (previewModalRef.value && tempTemplate) {
      await previewModalRef.value.show(tempTemplate, {}, {
        width: previewDialogWidth.value
      });
    } else {
      throw new Error('无法创建预览模板实例');
    }
  } catch (error) {
    const errorMessage = handleHiprintError(error);
    EleMessage.error('预览失败：' + errorMessage);
  }
};

const deleteTemplate = async (template) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模板"${template.templateName}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    // 调用删除API
    await deleteTemplateApi(template.id);
    EleMessage.success('删除成功');
    await loadTemplateList();

  } catch (error) {
    if (error !== 'cancel') {
      EleMessage.error('删除失败：' + error.message);
    }
  }
};

const initDesigner = async (template = null) => {
  try {
    console.log('开始初始化设计器...', template ? '编辑模式' : '新建模式');

    // 初始化 hiprint 插件
    await initHiprintPlugin();

    // 清理之前的实例
    if (hiprintTemplate) {
      try {
        cleanupTemplate(hiprintTemplate);
      } catch (e) {
        console.warn('清理旧模板实例失败:', e);
      }
      hiprintTemplate = null;
    }

    // 设置元素选中和变更回调
    setElementSelectCallback((element) => {
      console.log('元素被选中:', element);
      // 这里可以添加自定义的元素选中处理逻辑
    });

    setElementChangeCallback((element, property, value) => {
      console.log('元素属性变更:', element, property, value);
      // 这里可以添加自定义的元素变更处理逻辑
    });

    // 创建模板实例 - 关键修复：在构造函数中传入配置
    const templateOptions = {
      settingContainer: '#PrintElementOptionSetting',
      paginationContainer: '.hiprint-printPagination',
      dataMode: 1,
      history: true,
      // 图片选择功能（可选）
      onImageChooseClick: (target) => {
        console.log('图片选择被点击:', target);
        // 这里可以添加图片选择逻辑
      }
    };

    if (template && template.layoutConfig) {
      console.log('加载现有模板数据...');
      console.log('原始模板数据:', template.layoutConfig);
      try {
        const templateData = JSON.parse(template.layoutConfig);
        console.log('解析的模板数据:', templateData);

        // 调试模板数据结构
        debugTemplateData(templateData, '编辑模式加载');

        // 验证模板数据结构并进行适配
        let validTemplateData = null;
        if (templateData) {
          // 检查是否是hiprint标准格式
          if (templateData.panels || templateData.template) {
            validTemplateData = templateData;
          }
          // 检查是否是直接的面板数组格式
          else if (Array.isArray(templateData)) {
            validTemplateData = { panels: templateData };
          }
          // 检查是否包含其他可能的数据结构
          else if (templateData.printElements || templateData.width || templateData.height) {
            // 可能是单个面板的数据，包装成标准格式
            validTemplateData = { panels: [templateData] };
          }
        }

        if (validTemplateData) {
          console.log('使用有效的模板数据创建模板:', validTemplateData);
          hiprintTemplate = createPrintTemplate(validTemplateData, templateOptions);
          console.log('模板创建成功，面板数量:', hiprintTemplate.panels?.length || 0);
        } else {
          console.warn('模板数据结构无效，创建空模板');
          hiprintTemplate = createPrintTemplate(null, templateOptions);
        }

      } catch (parseError) {
        console.error('解析模板数据失败:', parseError);
        EleMessage.error('模板数据格式错误，将创建新模板');
        hiprintTemplate = createPrintTemplate(null, templateOptions);
      }
    } else {
      console.log('创建新模板...');
      hiprintTemplate = createPrintTemplate(null, templateOptions);
    }

    // 确保模板有面板
    if (!hiprintTemplate.panels || hiprintTemplate.panels.length === 0) {
      console.log('模板没有面板，创建默认面板...');
      if (typeof hiprintTemplate.addPrintPanel === 'function') {
        const panel = hiprintTemplate.addPrintPanel();
        console.log('创建了新面板:', panel);
      } else {
        console.warn('模板没有addPrintPanel方法');
      }
    }

    // 等待DOM容器准备就绪后再渲染设计器
    await nextTick();

    // 确保设计器容器存在
    const designerContainer = document.querySelector('#hiprint-panel-center');
    if (!designerContainer) {
      throw new Error('设计器容器 #hiprint-panel-center 不存在');
    }

    console.log('设置设计器...');
    hiprintTemplate.design('#hiprint-panel-center', designerConfig);

    // 验证设计器是否正确渲染
    setTimeout(() => {
      const renderedElements = designerContainer.querySelectorAll('.hiprint-printElement');
      console.log(`设计器渲染完成，包含 ${renderedElements.length} 个元素`);

      if (template && renderedElements.length === 0) {
        console.warn('编辑模式下没有渲染出任何元素，可能存在数据回显问题');
      }
    }, 500);

    // 初始化纸张设置回显
    if (template && hiprintTemplate) {
      try {
        let paperInfo = null;

        // 优先使用保存的pageSettings
        if (template.pageSettings) {
          try {
            const savedPageSettings = JSON.parse(template.pageSettings);
            console.log('从模板中恢复纸张设置:', savedPageSettings);
            paperInfo = {
              width: savedPageSettings.paperInfo?.width,
              height: savedPageSettings.paperInfo?.height,
              paperType: savedPageSettings.paperInfo?.type,
              orientation: savedPageSettings.orientation,
              isCustom: savedPageSettings.paperInfo?.type === 'custom'
            };
          } catch (parseError) {
            console.warn('解析保存的纸张设置失败:', parseError);
          }
        }

        // 如果没有保存的设置，则从hiprint模板获取
        if (!paperInfo) {
          paperInfo = getTemplatePaperInfo(hiprintTemplate);
          console.log('从hiprint模板获取纸张信息:', paperInfo);
        }

        // 等待PaperSettings组件准备就绪后初始化
        nextTick(() => {
          if (paperSettingsRef.value && typeof paperSettingsRef.value.initializePaperSettings === 'function') {
            paperSettingsRef.value.initializePaperSettings(paperInfo);
            console.log('纸张设置回显完成');
          } else {
            console.warn('PaperSettings组件未准备就绪或缺少初始化方法');
          }
        });
      } catch (error) {
        console.error('初始化纸张设置回显失败:', error);
      }
    }

    // 检测 API 信息（调试用）
    const apiInfo = detectHiprintAPI();
    console.log('设计器 API 信息:', apiInfo);

    // 构建可拖拽元素 - 使用Promise确保正确完成
    console.log('准备构建拖拽元素...');

    // 等待DOM完全渲染后再构建拖拽元素
    setTimeout(async () => {
      try {
        console.log('开始构建拖拽元素...');

        const result = await buildDraggableElements('.ep-draggable-item');
        console.log('拖拽元素构建结果:', result);

        // 验证拖拽元素
        const draggableElements = document.querySelectorAll('.ep-draggable-item');
        console.log(`找到 ${draggableElements.length} 个可拖拽元素`);

        draggableElements.forEach((el, index) => {
          console.log(`元素 ${index}:`, {
            tid: el.getAttribute('tid'),
            className: el.className,
            text: el.textContent.trim()
          });
        });

        // 检查hiprint的provider状态
        debugHiprintStatus();
      } catch (buildError) {
        console.error('构建拖拽元素失败:', buildError);
      }
    }, 1000); // 适当的延迟确保DOM完全渲染

  } catch (error) {
    const errorMessage = handleHiprintError(error);
    console.error('初始化设计器失败:', error);
    EleMessage.error('初始化设计器失败：' + errorMessage);
  }
};



// 调试模板数据结构
const debugTemplateData = (templateData, source = 'unknown') => {
  console.log(`=== 调试模板数据 (来源: ${source}) ===`);
  console.log('原始数据:', templateData);

  if (!templateData) {
    console.log('数据为空');
    return;
  }

  console.log('数据类型:', typeof templateData);
  console.log('数据键:', Object.keys(templateData));

  if (templateData.panels) {
    console.log('面板数量:', templateData.panels.length);
    templateData.panels.forEach((panel, index) => {
      console.log(`面板 ${index}:`, {
        width: panel.width,
        height: panel.height,
        elementsCount: panel.printElements?.length || 0
      });

      if (panel.printElements) {
        panel.printElements.forEach((element, elemIndex) => {
          console.log(`  元素 ${elemIndex}:`, {
            type: element.type,
            left: element.options?.left,
            top: element.options?.top,
            width: element.options?.width,
            height: element.options?.height,
            text: element.options?.text || element.options?.title
          });
        });
      }
    });
  }

  if (templateData.template) {
    console.log('包含template属性:', templateData.template);
  }

  console.log('=== 调试结束 ===');
};

// 确保模板有面板可用
const ensurePanelExists = () => {
  if (!hiprintTemplate) {
    console.warn('模板实例不存在，无法创建面板');
    return false;
  }

  if (!hiprintTemplate.panels || hiprintTemplate.panels.length === 0) {
    console.log('模板没有面板，尝试创建...');
    if (typeof hiprintTemplate.addPrintPanel === 'function') {
      const panel = hiprintTemplate.addPrintPanel();
      console.log('创建了新面板:', panel);
      return true;
    } else {
      console.warn('模板没有addPrintPanel方法，无法创建面板');
      return false;
    }
  }

  console.log('模板已有面板:', hiprintTemplate.panels.length);
  return true;
};

// 调试hiprint状态
const debugHiprintStatus = () => {
  console.log('=== hiprint 状态调试 ===');

  if (!hiprintTemplate) {
    console.error('hiprintTemplate 未初始化');
    return;
  }

  console.log('hiprintTemplate:', hiprintTemplate);
  console.log('panels:', hiprintTemplate.panels);

  // 检查provider
  const hiprint = getHiprintInstance();
  if (hiprint) {
    console.log('hiprint实例:', hiprint);
    console.log('PrintElementTypeManager:', hiprint.PrintElementTypeManager);

    // 检查已注册的元素类型
    if (hiprint.PrintElementTypeManager && hiprint.PrintElementTypeManager.getElementTypes) {
      const elementTypes = hiprint.PrintElementTypeManager.getElementTypes();
      console.log('已注册的元素类型:', elementTypes);
    }
  }

  // 检查jQuery
  if (window.$) {
    console.log('jQuery 可用，版本:', window.$.fn.jquery);
    const $draggableElements = window.$('.ep-draggable-item');
    console.log('jQuery找到的拖拽元素数量:', $draggableElements.length);
  } else {
    console.warn('jQuery 不可用');
  }

  console.log('=== 调试结束 ===');
};


const getTemplateTypeText = (type) => {
  const typeMap = {
    'CUSTOM': '自定义',
    'SYSTEM': '系统'
  };
  return typeMap[type] || type;
};

const formatTime = (time) => {
  return time ? dayjs(time).format('YYYY-MM-DD HH:mm') : '';
};

// 处理模板名称变化
const handleTemplateNameChange = (newName) => {
  currentTemplateName.value = newName;
  if (currentTemplate.value) {
    currentTemplate.value.templateName = newName;
  }
};



// 处理纸张变化
const handlePaperChange = (paperInfo) => {
  console.log('纸张设置已更改:', paperInfo);
};

// 处理纸张方向变化
const handleOrientationChange = (orientation) => {
  console.log('纸张方向已更改:', orientation);
};



// 生命周期
onMounted(async () => {
  await loadTemplateList();
  await loadAvailableFields();
});

onUnmounted(() => {
  if (hiprintTemplate) {
    cleanupTemplate(hiprintTemplate);
  }
});
</script>

<style scoped>
.label-hiprint-page {
  padding: 16px;
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.header-card {
  margin-bottom: 16px;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: 600;
}

.template-list {
  margin-top: 16px;
}

.template-card {
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 16px;
}

.template-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.template-card.active {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.template-info {
  margin-bottom: 12px;
}

.template-name {
  font-weight: 600;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.template-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
}

.template-actions {
  display: flex;
  gap: 8px;
}

.designer-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许flex子项收缩 */
}

.designer-card :deep(.el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
  min-height: 0;
}

.designer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.designer-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.hiprint-container {
  display: flex;
  flex: 1;
  height: calc(100vh - 380px); /* 调整为更精确的高度计算 */
  min-height: 500px; /* 降低最小高度以适应小屏幕 */
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-top: 16px;
}

.hiprint-panel-left {
  width: 200px;
  border-right: 1px solid #ddd;
  background: #f5f5f5;
  overflow-y: auto;
}

.hiprint-panel-center {
  flex: 1;
  background: #fff;
  position: relative;
}

.hiprint-panel-right {
  width: 320px;
  border-left: 1px solid #ddd;
  background: #f5f5f5;
  overflow-y: auto;
}

/* hiprint官方属性面板样式 */
.hiprint-option-setting {
  height: 100%;
  overflow-y: auto;
  padding: 16px;
}

/* 确保hiprint属性面板样式正确显示 */
#PrintElementOptionSetting {
  height: 100%;
  overflow-y: auto;
}

/* hiprint属性面板内部样式调整 */
#PrintElementOptionSetting .hiprint-option-item {
  margin-bottom: 12px;
}

#PrintElementOptionSetting .hiprint-option-item-label {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
  display: block;
}

#PrintElementOptionSetting .hiprint-option-item-content {
  width: 100%;
}

#PrintElementOptionSetting input,
#PrintElementOptionSetting select,
#PrintElementOptionSetting textarea {
  width: 100%;
  padding: 4px 8px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 12px;
  box-sizing: border-box;
}

#PrintElementOptionSetting .hiprint-option-tabs {
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 16px;
  display: flex;
}

#PrintElementOptionSetting .hiprint-option-tab {
  padding: 8px 16px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  font-size: 14px;
  background: none;
  border: none;
  outline: none;
}

#PrintElementOptionSetting .hiprint-option-tab.active {
  color: #409eff;
  border-bottom-color: #409eff;
}

#PrintElementOptionSetting .hiprint-option-tab:hover {
  color: #409eff;
}

/* 属性面板按钮样式 */
#PrintElementOptionSetting button {
  padding: 4px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: #fff;
  cursor: pointer;
  font-size: 12px;
  margin-right: 8px;
  margin-bottom: 8px;
}

#PrintElementOptionSetting button:hover {
  border-color: #409eff;
  color: #409eff;
}

#PrintElementOptionSetting button.primary {
  background: #409eff;
  color: #fff;
  border-color: #409eff;
}

#PrintElementOptionSetting button.primary:hover {
  background: #66b1ff;
}

.element-library {
  padding: 16px;
}

.element-library h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
}

.element-group {
  margin-bottom: 16px;
}

.element-group h5 {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #666;
}

.element-item {
  padding: 8px 12px;
  margin-bottom: 4px;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.element-item:hover {
  background: #e6f7ff;
  border-color: #409eff;
}

.property-panel {
  padding: 16px;
}

.property-panel h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
}

/* 预览相关样式已移至PreviewModal组件中 */

/* 响应式设计 */
@media (max-height: 800px) {
  .hiprint-container {
    height: calc(100vh - 300px);
    min-height: 400px;
  }
}

@media (max-height: 600px) {
  .hiprint-container {
    height: calc(100vh - 250px);
    min-height: 350px;
  }
}

/* 确保在大屏幕上有更好的显示效果 */
@media (min-height: 1200px) {
  .hiprint-container {
    height: calc(100vh - 400px);
    min-height: 700px;
  }
}
</style>
