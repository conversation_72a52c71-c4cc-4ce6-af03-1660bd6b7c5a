# 标签模板打印底纸大小功能测试指南

## 功能说明

**重要概念区分**：
- **标签设计器中的纸张大小** = 单个标签的大小（用于设计标签内容）
- **保存表单中的底纸大小** = 整张打印纸的大小（用于批量打印时的纸张设置）

## 测试环境准备

1. 启动前端开发服务器
2. 启动后端服务
3. 确保数据库连接正常

## 测试步骤

### 1. 测试标签模板设计页面

#### 1.1 创建新模板
1. 访问 `/bank-note/label-hiprint` 页面
2. 点击"新建模板"按钮
3. 在hiprint设计器中设计单个标签的内容和大小
4. 点击"保存模板"按钮
5. 在保存表单中：
   - 填写模板名称
   - 在"打印底纸设置"部分设置整张纸的大小
   - 可以使用快捷按钮（A4、A5等）或手动输入宽高
   - 验证输入验证规则（10-1000mm范围）

#### 1.2 编辑已有模板
1. 选择一个已保存的模板进行编辑
2. 检查保存表单中的底纸大小是否正确回显
3. 修改底纸大小设置后重新保存
4. 验证修改是否生效

### 2. 测试批量打印页面

#### 2.1 预览功能测试
1. 访问 `/bank-note/batch-print` 页面
2. 选择包含不同底纸大小设置的模板
3. 选择一些钱币数据
4. 点击"打印标签"按钮
5. 在预览窗口中检查：
   - 预览容器的尺寸是否与模板的底纸大小设置匹配
   - 页面布局是否正确
   - 控制台是否输出底纸设置解析信息
   - 多个标签是否正确排列在底纸上

#### 2.2 打印功能测试
1. 在预览窗口中点击"打印"按钮
2. 检查打印预览窗口：
   - 页面尺寸是否与设置的底纸大小匹配
   - CSS @page 规则是否生效
   - 打印机设置是否显示正确的纸张大小

### 3. 数据验证测试

#### 3.1 数据库验证
检查 `LABEL_TEMPLATE` 表中的 `PAGE_SETTINGS` 字段：
```sql
SELECT TEMPLATE_NAME, PAGE_SETTINGS FROM LABEL_TEMPLATE WHERE PAGE_SETTINGS IS NOT NULL;
```

预期的数据格式：
```json
{
  "paperWidth": 210,
  "paperHeight": 297,
  "paperType": "custom",
  "timestamp": "2025-01-30T10:30:00.000Z"
}
```

#### 3.2 API响应验证
使用浏览器开发者工具检查：
1. 模板保存API (`/api/label-design/template`) 的请求体是否包含 `pageSettings`
2. 打印数据生成API (`/api/batch-print/generate`) 的响应是否包含 `pageSettings`

## 预期结果

### 正常情况
- 纸张设置能够正确保存和恢复
- 预览容器尺寸与纸张设置匹配
- 打印时使用正确的纸张尺寸
- 控制台无错误信息

### 异常处理
- 解析纸张设置失败时使用默认A4尺寸
- 缺少纸张设置时不影响基本功能
- 错误信息能够正确显示

## 常见问题排查

### 1. 纸张设置未保存
- 检查 `paperSettingsRef.value` 是否存在
- 检查 `getCurrentPaper()` 和 `getOrientation()` 方法是否正常工作

### 2. 预览尺寸不正确
- 检查 `parsePaperSettings()` 函数是否正确解析
- 检查 `paperSettings.value` 的值
- 检查 `getPrintPageStyle()` 函数的计算逻辑

### 3. 打印尺寸不正确
- 检查打印HTML中的CSS样式
- 检查 `@page` 规则是否正确设置
- 检查浏览器打印预览中的页面设置

## 调试技巧

1. 在浏览器控制台中查看相关日志：
   ```javascript
   // 查看纸张设置
   console.log('纸张设置:', paperSettings.value);
   
   // 查看页面配置
   console.log('页面配置:', pageConfig.value);
   ```

2. 检查DOM元素的实际尺寸：
   ```javascript
   // 检查预览容器尺寸
   const previewPage = document.querySelector('.print-page');
   console.log('预览页面尺寸:', previewPage.getBoundingClientRect());
   ```

3. 验证API数据：
   ```javascript
   // 在网络面板中检查API响应
   // 确保 pageSettings 字段存在且格式正确
   ```
