/**
 * 标签模板打印底纸大小功能演示脚本
 *
 * 重要概念：
 * - 标签设计器中的纸张大小 = 单个标签的大小
 * - 打印底纸大小 = 整张打印纸的大小（用于批量打印）
 */

// 1. 打印底纸设置数据结构示例
const printPaperSettingsExamples = {
  // A4打印底纸
  a4Paper: {
    paperWidth: 210,   // 底纸宽度(mm)
    paperHeight: 297,  // 底纸高度(mm)
    paperType: 'custom',
    timestamp: '2025-01-30T10:30:00.000Z'
  },

  // A5打印底纸
  a5Paper: {
    paperWidth: 148,
    paperHeight: 210,
    paperType: 'custom',
    timestamp: '2025-01-30T10:30:00.000Z'
  },

  // 自定义打印底纸
  customPaper: {
    paperWidth: 100,   // 小尺寸底纸
    paperHeight: 150,
    paperType: 'custom',
    timestamp: '2025-01-30T10:30:00.000Z'
  },

  // 大尺寸打印底纸
  largePaper: {
    paperWidth: 420,   // A3横向
    paperHeight: 297,
    paperType: 'custom',
    timestamp: '2025-01-30T10:30:00.000Z'
  }
};

// 2. 模板保存时的数据结构
const templateSaveData = {
  templateName: '测试标签模板',
  templateType: 'CUSTOM',
  layoutConfig: JSON.stringify({
    panels: [
      {
        index: 0,
        width: 50,    // 单个标签的宽度(mm)
        height: 30,   // 单个标签的高度(mm)
        printElements: [
          // 标签内容元素...
        ]
      }
    ]
  }),
  pageSettings: JSON.stringify(printPaperSettingsExamples.a4Paper), // 打印底纸设置
  isDefault: false,
  description: '带有打印底纸设置的测试模板',
  status: 'ACTIVE'
};

// 3. 批量打印数据结构
const printDataWithPaperSettings = {
  totalCount: 10,
  templateId: 'template-123',
  templateName: '测试标签模板',
  layoutConfig: {
    panels: [/* 单个标签的设计数据 */]
  },
  pageSettings: JSON.stringify(printPaperSettingsExamples.a4Paper), // 打印底纸设置
  processedTemplates: [
    // 处理后的模板数据（每个钱币对应一个）...
  ],
  items: [
    // 钱币数据...
  ]
};

// 4. 前端解析打印底纸设置的工具函数
function parsePrintPaperSettings(pageSettingsJson) {
  try {
    if (!pageSettingsJson) {
      return null;
    }

    const settings = JSON.parse(pageSettingsJson);

    // 验证数据结构
    if (!settings.paperWidth || !settings.paperHeight) {
      console.warn('打印底纸设置数据不完整');
      return null;
    }

    return {
      paperWidth: settings.paperWidth,
      paperHeight: settings.paperHeight,
      paperType: settings.paperType || 'custom',
      timestamp: settings.timestamp
    };
  } catch (error) {
    console.error('解析打印底纸设置失败:', error);
    return null;
  }
}

// 5. 计算预览容器样式的工具函数
function calculatePreviewStyle(paperSettings) {
  // 默认A4尺寸
  let width = 210;
  let height = 297;

  if (paperSettings && paperSettings.paperInfo) {
    width = paperSettings.paperInfo.width;
    height = paperSettings.paperInfo.height;
  }

  return {
    width: `${width}mm`,
    minHeight: `${height}mm`,
    maxWidth: `${width}mm`,
    aspectRatio: `${width} / ${height}`
  };
}

// 6. 生成打印CSS的工具函数
function generatePrintCSS(paperSettings) {
  let width = 210;
  let height = 297;

  if (paperSettings && paperSettings.paperInfo) {
    width = paperSettings.paperInfo.width;
    height = paperSettings.paperInfo.height;
  }

  return `
    @media print {
      @page {
        size: ${width}mm ${height}mm;
        margin: 0;
      }

      .print-page {
        width: ${width}mm;
        height: ${height}mm;
        page-break-after: always;
      }

      .print-page:last-child {
        page-break-after: auto;
      }
    }

    @media screen {
      .print-page {
        width: ${width}mm;
        min-height: ${height}mm;
        border: 1px solid #ddd;
        margin: 20px auto;
        background: white;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      }
    }
  `;
}

// 7. 使用示例
console.log('=== 纸张大小功能演示 ===');

// 解析纸张设置
const parsedSettings = parsePaperSettings(JSON.stringify(paperSettingsExamples.customSize));
console.log('解析的纸张设置:', parsedSettings);

// 计算预览样式
const previewStyle = calculatePreviewStyle(paperSettingsExamples.customSize);
console.log('预览容器样式:', previewStyle);

// 生成打印CSS
const printCSS = generatePrintCSS(paperSettingsExamples.customSize);
console.log('打印CSS:', printCSS);

// 8. Vue组件中的使用示例
const vueComponentExample = `
// 在Vue组件中使用
export default {
  data() {
    return {
      paperSettings: null,
      pageConfig: {
        width: 210,
        height: 297
      }
    }
  },

  methods: {
    // 解析打印数据中的纸张设置
    parsePrintDataPaperSettings(printData) {
      if (printData.pageSettings) {
        this.paperSettings = JSON.parse(printData.pageSettings);
        this.updatePageConfig();
      }
    },

    // 更新页面配置
    updatePageConfig() {
      if (this.paperSettings && this.paperSettings.paperInfo) {
        this.pageConfig.width = this.paperSettings.paperInfo.width;
        this.pageConfig.height = this.paperSettings.paperInfo.height;
      }
    },

    // 获取预览样式
    getPreviewStyle() {
      return {
        width: this.pageConfig.width + 'mm',
        minHeight: this.pageConfig.height + 'mm'
      };
    }
  }
}
`;

console.log('Vue组件使用示例:', vueComponentExample);

export {
  paperSettingsExamples,
  templateSaveData,
  printDataWithPaperSettings,
  parsePaperSettings,
  calculatePreviewStyle,
  generatePrintCSS
};
